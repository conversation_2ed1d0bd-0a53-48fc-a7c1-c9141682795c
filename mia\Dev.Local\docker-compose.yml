services:
  traefik:
    image: traefik:v3.4.1
    container_name: traefik
#    labels:
#      - traefik.enable=true
#      - traefik.http.routers.traefik.rule=Host(`localhost:8081`)
#      - traefik.http.routers.traefik.service=api@internal
    ports:
      - "8080:80"
      - "8081:8080"
    extra_hosts:
      - "external-ip:host-gateway"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik/traefik.yml:/etc/traefik/traefik.yml:ro
      - ./traefik/dynamic.yml:/etc/traefik/dynamic.yml:ro
    healthcheck:
      test: [ "CMD-SHELL", "traefik healthcheck"]

  ui:
    image: ${UI_IMAGE:-237029655182.dkr.ecr.ca-central-1.amazonaws.com/cgpt-ui}:${UI_VERSION:-latest}
    container_name: ui
    extra_hosts:
      - "external-ip:host-gateway"
    environment:
      - ENV_MODE=docker
      - API_URL=http://localhost:8080
      - ENTRA_CLIENT_ID=a455bf9a-1b91-41a8-a340-5bbb5eaef2dd
      - ENTRA_TENANT_ID=0bdbe027-8f50-4ec3-843f-e27c41a63957
      - CGPT_ROLE_ADMIN=CDVGS_CGPT_ADMINISTRATOR
      - CGPT_ROLE_ANDOC=CDVGS_CGPT_MOD_ANDOC
      - CGPT_ROLE_BNQINVT=CDVGS_CGPT_MOD_BNQINVT
      - CGPT_ROLE_TALPERF=CDVGS_CGPT_MOD_TALPERF
      - CGPT_ROLE_TRADUC=CDVGS_CGPT_MOD_TRADUC
      - CGPT_ROLE_USER=CDVGS_CGPT_USER
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:80 || exit 1"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 20s

  emp:
    image: ${EMP_IMAGE:-237029655182.dkr.ecr.ca-central-1.amazonaws.com/cgpt-emp}:${EMP_VERSION:-latest}
    container_name: emp
    environment:
      - ENV_MODE=DOCKER
      - EXPOSE_DOCS=true
      - ENTRA_AUDIENCE=a455bf9a-1b91-41a8-a340-5bbb5eaef2dd
      - ENTRA_AUTHORITY=0bdbe027-8f50-4ec3-843f-e27c41a63957
      - CGPT_MS_GRAPH_TENANT_ID=0bdbe027-8f50-4ec3-843f-e27c41a63957
      - CGPT_MS_GRAPH_CLIENT_ID=6b16df87-c57d-456f-8d0b-64922fb1316f
      - CGPT_MS_GRAPH_SECRET_ARN=arn:aws:secretsmanager:ca-central-1:237029655182:secret:MPV/CDPQ-CGPT-MSGRAPH-DV-4tMU1i
      - CGPT_MS_GRAPH_CLIENT_SECRET=****************************************
      - CGPT_SHARED_JWT_SECRET=qqkD4ufZkTcoAJdigSG0ORDvgfjDBgcyQXOWiZRC
      - CGPT_PERMISSIONS_GROUPS=CDVGS_CGPT_MOD_BNQINVT,CDVGS_CGPT_MOD_TRADUC,CDVGS_CGPT_MOD_TALPERF,CDVGS_CGPT_USER,CDVGS_CGPT_MOD_ANDOC,CDVGS_CGPT_ADMINISTRATOR
      - PG_DATABASE_MODE=AWS
      - PG_HOSTNAME=rdsa-pgs-cluster-dvcgpt01.cluster-chg4mcqseqk6.ca-central-1.rds.amazonaws.com
      - PG_PORT=5432
      - PG_SSL_MODE=VerifyFull
      - RDS_APPLICATION_USER_ID=iam_dvcgpt_appsvc
      - AWS_PROFILE=ESG-DV-PowerUser-SSO
    volumes:
      - ~/.aws:/root/.aws:ro
    extra_hosts:
      - "external-ip:host-gateway"
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 20s

  andoc:
    image: ${ANDOC_IMAGE:-237029655182.dkr.ecr.ca-central-1.amazonaws.com/cgpt-andoc}:${ANDOC_VERSION:-latest}
    container_name: andoc
    environment:
      - ALLOWED_ORIGINS=http://localhost:8080
      - AZURE_OPENAI_API_VERSION=2024-06-01
      - AZURE_OPENAI_AUTHORITY=https://login.microsoftonline.com/0bdbe027-8f50-4ec3-843f-e27c41a63957
      - AZURE_OPENAI_CLIENT_ID=3ac72c1f-36a5-453d-b612-ef23fb6babb4
      - AZURE_OPENAI_CLIENT_SECRET=****************************************
      - AZURE_OPENAI_SECRET_ARN=arn:aws:secretsmanager:ca-central-1:237029655182:secret:MPV/CDP-ALZ-CGPT-DV-AI-CNC-CLIENT-z0jfx7
      - AZURE_OPENAI_EMBEDDING_MODEL=text-embedding-3-large
      - AZURE_OPENAI_ENDPOINT=https://apim-alz-shsvc-pr-01-c-cnc.cdpq.cloud/cgpt/dv/aoai
      - AZURE_OPENAI_GEN_MODEL=gpt-4o-mini
      - AZURE_OPENAI_SCOPES=api://f262115a-fc0f-4612-9009-a68e124ee79f/.default
      - CGPT_SHARED_JWT_SECRET=qqkD4ufZkTcoAJdigSG0ORDvgfjDBgcyQXOWiZRC
      - DATABASE_NAME=cgpt
      - ENTRA_AUDIENCE=a455bf9a-1b91-41a8-a340-5bbb5eaef2dd"
      - ENTRA_AUTHORITY=0bdbe027-8f50-4ec3-843f-e27c41a63957
      - ENTRA_CLIENT_ID=a455bf9a-1b91-41a8-a340-5bbb5eaef2dd
      - ENV_MODE=DOCKER
      - EXPOSE_DOCS=true
      - PG_DATABASE_MODE=AWS
      - PG_HOSTNAME=rdsa-pgs-cluster-dvcgpt01.cluster-chg4mcqseqk6.ca-central-1.rds.amazonaws.com
      - PG_PORT=5432
      - PG_SSL_MODE=VerifyFull
      - RDS_APPLICATION_USER_ID=iam_dvcgpt_appsvc
      - AWS_PROFILE=ESG-DV-PowerUser-SSO
    volumes:
      - ~/.aws:/root/.aws:ro
    extra_hosts:
      - "external-ip:host-gateway"
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 20s

  bnqinvt:
#    image: 237029655182.dkr.ecr.ca-central-1.amazonaws.com/cgpt-bnqinvt:latest
    image: ${BNQINVT_IMAGE:-cdpq.jfrog.io/cgpt-docker-builds/bnqinvt}:${BNQINVT_VERSION:-latest}
    container_name: bnqinvt
    environment:
      - ALLOWED_ORIGINS=http://localhost:8080
      - CGPT_SHARED_JWT_SECRET=qqkD4ufZkTcoAJdigSG0ORDvgfjDBgcyQXOWiZRC
      - DATABASE_NAME=cgpt
      - ENTRA_AUDIENCE=a455bf9a-1b91-41a8-a340-5bbb5eaef2dd
      - ENTRA_AUTHORITY=0bdbe027-8f50-4ec3-843f-e27c41a63957
      - ENTRA_CLIENT_ID=a455bf9a-1b91-41a8-a340-5bbb5eaef2dd
      - ENV_MODE=DOCKER
      - EXPOSE_DOCS=true
      - PG_DATABASE_MODE=AWS
      - PG_HOSTNAME=rdsa-pgs-cluster-dvcgpt01.cluster-chg4mcqseqk6.ca-central-1.rds.amazonaws.com
      - PG_PORT=5432
      - PG_SSL_MODE=VerifyFull
      - RDS_APPLICATION_USER_ID=iam_dvcgpt_appsvc
      - AWS_PROFILE=ESG-DV-PowerUser-SSO
    volumes:
      - ~/.aws:/root/.aws:ro
    extra_hosts:
      - "external-ip:host-gateway"
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 20s

  traduc:
    image: ${TRADUC_IMAGE:-237029655182.dkr.ecr.ca-central-1.amazonaws.com/cgpt-traduc}:${TRADUC_VERSION:-latest}
    container_name: traduc
    environment:
      - ALLOWED_ORIGINS=http://localhost:8080
      - AZURE_OPENAI_API_VERSION=2024-06-01
      - AZURE_OPENAI_AUTHORITY=https://login.microsoftonline.com/0bdbe027-8f50-4ec3-843f-e27c41a63957
      - AZURE_OPENAI_CLIENT_ID=3ac72c1f-36a5-453d-b612-ef23fb6babb4
      - AZURE_OPENAI_CLIENT_SECRET=****************************************
      - AZURE_OPENAI_EMBEDDING_MODEL=text-embedding-3-large
      - AZURE_OPENAI_ENDPOINT=https://apim-alz-shsvc-pr-01-c-cnc.cdpq.cloud/cgpt/dv/aoai
      - AZURE_OPENAI_GEN_MODEL=gpt-4o-mini
      - AZURE_OPENAI_SCOPES=api://f262115a-fc0f-4612-9009-a68e124ee79f/.default
      - CGPT_SHARED_JWT_SECRET=qqkD4ufZkTcoAJdigSG0ORDvgfjDBgcyQXOWiZRC
      - DATABASE_NAME=cgpt
      - ENTRA_AUDIENCE=a455bf9a-1b91-41a8-a340-5bbb5eaef2dd"
      - ENTRA_AUTHORITY=0bdbe027-8f50-4ec3-843f-e27c41a63957
      - ENTRA_CLIENT_ID=a455bf9a-1b91-41a8-a340-5bbb5eaef2dd
      - ENV_MODE=DOCKER
      - EXPOSE_DOCS=true
      - PG_DATABASE_MODE=AWS
      - PG_HOSTNAME=rdsa-pgs-cluster-dvcgpt01.cluster-chg4mcqseqk6.ca-central-1.rds.amazonaws.com
      - PG_PORT=5432
      - PG_SSL_MODE=VerifyFull
      - RDS_APPLICATION_USER_ID=iam_dvcgpt_appsvc
      - AWS_PROFILE=ESG-DV-PowerUser-SSO
    volumes:
      - ~/.aws:/root/.aws:ro
    extra_hosts:
      - "external-ip:host-gateway"
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 20s
  
  talperftraitement:
    image: ${TALPERFTRAITEMENT_IMAGE:-237029655182.dkr.ecr.ca-central-1.amazonaws.com/cgpt-talperf-traitement}:${TALPERFTRAITEMENT_VERSION:-latest}
    container_name: talperftraitement
    environment:
      - ALLOWED_ORIGINS=http://localhost:8080
      - CGPT_SHARED_JWT_SECRET=qqkD4ufZkTcoAJdigSG0ORDvgfjDBgcyQXOWiZRC
      - DATABASE_NAME=cgpt
      - ENTRA_AUDIENCE=a455bf9a-1b91-41a8-a340-5bbb5eaef2dd
      - ENTRA_AUTHORITY=0bdbe027-8f50-4ec3-843f-e27c41a63957
      - ENTRA_CLIENT_ID=a455bf9a-1b91-41a8-a340-5bbb5eaef2dd
      - AZURE_OPENAI_CLIENT_ID=3ac72c1f-36a5-453d-b612-ef23fb6babb4
      - AZURE_OPENAI_CLIENT_SECRET=****************************************
      - AZURE_OPENAI_SECRET_ARN=arn:aws:secretsmanager:ca-central-1:237029655182:secret:MPV/CDP-ALZ-CGPT-DV-AI-CNC-CLIENT-z0jfx7
      - CGPT_SHAREPOINT_SECRET_ARN=arn:aws:secretsmanager:ca-central-1:237029655182:secret:MPV/CDPQ-CGPT-SHAREPOINT-01-DV-4rtJOh
      - CGPT_MSGRAPH_SECRET_ARN=arn:aws:secretsmanager:ca-central-1:237029655182:secret:MPV/CDPQ-CGPT-MSGRAPH-DV-4tMU1i
      - AZURE_OPENAI_AUTHORITY=https://login.microsoftonline.com/0bdbe027-8f50-4ec3-843f-e27c41a63957
      - AZURE_OPENAI_SCOPES=api://f262115a-fc0f-4612-9009-a68e124ee79f/.default
      - AZURE_OPENAI_ENDPOINT=https://apim-alz-shsvc-pr-01-c-cnc.cdpq.cloud/cgpt/dv/aoai
      - AZURE_OPENAI_API_VERSION=2024-06-01
      - AZURE_OPENAI_GEN_MODEL=gpt-4o-mini
      - AZURE_OPENAI_EMBEDDING_MODEL=text-embedding-3-large
      - ENV_MODE=DOCKER
      - EXPOSE_DOCS=true
      - PG_DATABASE_MODE=AWS
      - PG_HOSTNAME=rdsa-pgs-cluster-dvcgpt01.cluster-chg4mcqseqk6.ca-central-1.rds.amazonaws.com
      - PG_PORT=5432
      - PG_SSL_MODE=VerifyFull
      - RDS_APPLICATION_USER_ID=iam_dvcgpt_appsvc
      - AWS_PROFILE=ESG-DV-PowerUser-SSO
    volumes:
      - ~/.aws:/root/.aws:ro
    extra_hosts:
      - "external-ip:host-gateway"
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 20s

#  talperf-ingestion:
#    image: 237029655182.dkr.ecr.ca-central-1.amazonaws.com/cgpt-talperf-ingestion:latest
#    container_name: talperfing
#    environment:
#      - ALLOWED_ORIGINS=http://localhost:8080
#      - CGPT_SHARED_JWT_SECRET=qqkD4ufZkTcoAJdigSG0ORDvgfjDBgcyQXOWiZRC
#      - DATABASE_NAME=cgpt
#      - ENTRA_AUDIENCE=a455bf9a-1b91-41a8-a340-5bbb5eaef2dd
#      - ENTRA_AUTHORITY=0bdbe027-8f50-4ec3-843f-e27c41a63957
#      - ENTRA_CLIENT_ID=a455bf9a-1b91-41a8-a340-5bbb5eaef2dd
#      - AZURE_OPENAI_CLIENT_ID=3ac72c1f-36a5-453d-b612-ef23fb6babb4
#      - AZURE_OPENAI_CLIENT_SECRET=****************************************
#      - AZURE_OPENAI_AUTHORITY=https://login.microsoftonline.com/0bdbe027-8f50-4ec3-843f-e27c41a63957
#      - AZURE_OPENAI_SCOPES=api://f262115a-fc0f-4612-9009-a68e124ee79f/.default
#      - AZURE_OPENAI_ENDPOINT=https://apim-alz-shsvc-pr-01-c-cnc.cdpq.cloud/cgpt/dv/aoai
#      - AZURE_OPENAI_API_VERSION=2024-06-01
#      - AZURE_OPENAI_GEN_MODEL=gpt-4o-mini
#      - AZURE_OPENAI_EMBEDDING_MODEL=text-embedding-3-large
#      - ENV_MODE=DOCKER
#      - PG_DATABASE_MODE=AWS
#      - PG_HOSTNAME=rdsa-pgs-cluster-dvcgpt01.cluster-chg4mcqseqk6.ca-central-1.rds.amazonaws.com
#      - PG_PORT=5432
#      - PG_SSL_MODE=VerifyFull
#      - RDS_APPLICATION_USER_ID=iam_dvcgpt_appsvc
#      - AWS_PROFILE=ESG-DV-PowerUser-SSO
#    volumes:
#      - ~/.aws:/root/.aws:ro
#    extra_hosts:
#      - "external-ip:host-gateway"
#    healthcheck:
#      test: ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"]
#      interval: 30s
#      timeout: 5s
#      retries: 3
#      start_period: 20s
