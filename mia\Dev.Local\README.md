# Gestionnaire de Services et Configurations

Ce projet fournit un script PowerShell pour gérer divers services et configurations avec Docker Compose et Traefik.

## Structure du Projet

```
├── launch.ps1           # Script principal de gestion des services
├── docker-compose.yml   # Configuration Docker Compose
├── variables.txt        # Variables de configuration (origine des images)
├── tests.http          # Tests HTTP pour validation
├── traefik/            # Configuration Traefik
│   ├── traefik.yml     # Configuration principale Traefik
│   └── dynamic.yml     # Configuration dynamique Traefik
└── README.md           # Ce fichier
```

## Utilisation du Script Principal

### Syntaxe

```powershell
.\launch.ps1 [-r <service>] [-b <branche>] [-c <commande>] [-h]
```

### Paramètres

| Paramètre | Description | Valeur par défaut | Obligatoire |
|-----------|-------------|-------------------|-------------|
| `-r` | Le service à redémarrer | - | Non |
| `-b` | La branche Git à utiliser (ajoute "-SNAPSHOT") | - | Non |
| `-c` | La commande à exécuter | `start` | Non |
| `-h` | Afficher l'aide avec Get-Help | - | Non |

### Commandes Disponibles

- `start` : Démarrer les services (défaut)
- `stop` : Arrêter les services
- `sso` : Connexion AWS SSO
- `id` : Afficher l'identité AWS actuelle
- `dk` : Connexion Docker à AWS ECR
- `jfrog` : Connexion Docker à JFrog
- `help` : Afficher l'aide

### Exemples d'Utilisation

```powershell
# Démarrer les services (par défaut)
.\launch.ps1

# Démarrer les services explicitement
.\launch.ps1 -c start

# Redémarrer un service spécifique
.\launch.ps1 -r "andoc"

# Utiliser une branche spécifique (ajoute automatiquement "-SNAPSHOT")
.\launch.ps1 -b "feature/nouvelle-fonctionnalite"

# Arrêter tous les services
.\launch.ps1 -c stop

# Connexion AWS SSO
.\launch.ps1 -c sso

# Vérifier l'identité AWS
.\launch.ps1 -c id

# Connexion Docker à AWS ECR
.\launch.ps1 -c dk

# Connexion Docker à JFrog
.\launch.ps1 -c jfrog

# Afficher l'aide complète
.\launch.ps1 -h
```

## Configuration des Variables

Le fichier `variables.txt` contient les variables de configuration qui permettent de **modifier l'origine des images Docker** et d'autres paramètres du système. Ce fichier est automatiquement chargé au démarrage du script.

### Format du fichier variables.txt

```text
# Configuration des images Docker
IMAGE_REGISTRY=registry.example.com
IMAGE_TAG=latest
BASE_IMAGE_NAME=myapp

# Configuration pour le déploiement de branches
BACKEND_IMAGE=237029655182.dkr.ecr.ca-central-1.amazonaws.com/backend
FRONTEND_IMAGE=237029655182.dkr.ecr.ca-central-1.amazonaws.com/frontend

# Configuration AWS
AWS_REGION=ca-central-1
AWS_PROFILE=ESG-DV-PowerUser-SSO

# Autres variables d'environnement
COMPOSE_PROFILES=qa,lv,jc,int,mail,mon
```

### Modification de l'Origine des Images

Pour changer l'origine des images Docker, modifiez les variables suivantes dans `variables.txt` :

- `IMAGE_REGISTRY` : Registre Docker à utiliser (ex: `cdpq.jfrog.io`, `237029655182.dkr.ecr.ca-central-1.amazonaws.com`)
- `IMAGE_TAG` : Tag de l'image à déployer (ex: `latest`, `v1.2.3`)
- `BASE_IMAGE_NAME` : Nom de base de l'image
- `BACKEND_IMAGE` : Image complète du backend pour les déploiements de branche
- `FRONTEND_IMAGE` : Image complète du frontend pour les déploiements de branche

**Note** : Quand vous utilisez le paramètre `-b` pour déployer une branche, le script ajoute automatiquement `-SNAPSHOT` au nom de la branche pour former le tag de l'image.

## Configuration Docker Compose

Le fichier `docker-compose.yml` définit l'architecture des services. Il utilise les variables définies dans `variables.txt` pour configurer dynamiquement les images et les paramètres.

## Configuration Traefik

### traefik/traefik.yml
Configuration principale du reverse proxy Traefik incluant :
- Points d'entrée HTTP/HTTPS
- Configuration des certificats SSL
- Paramètres de routage

### traefik/dynamic.yml
Configuration dynamique pour :
- Routage des services
- Middlewares
- Certificats SSL automatiques

## Configuration des Ports pour Développement Local

Cette section détaille les ports que les modules locaux doivent utiliser pour être correctement traités par Traefik. Le système utilise une configuration de failover où Traefik tente d'abord de router vers les services locaux (external-ip) avant de basculer vers les conteneurs Docker.

### Ports des Services Locaux

Pour que Traefik puisse router correctement vers vos services en développement local, utilisez les ports suivants :

| Service | Port Local | Port Docker | URL de Routage | Health Check |
|---------|------------|-------------|----------------|--------------|
| **andoc** | `8000` | `8000` | `/andoc` | `/health` |
| **bnqinvt** | `8001` | `8000` | `/bnqinvt` | `/health` |
| **emp** | `8004` | `8000` | `/emp` | `/health` |
| **traduc** | `8005` | `8000` | `/traduc` | `/health` |
| **talperftraitement** | `8006` | `8000` | `/talperftraitement` | `/health` |
| **ui** | `4200` | `80` | `/` (racine) | `/` |

### Configuration Failover

Le système Traefik est configuré avec un mécanisme de failover automatique :

1. **Service Host (Priorité 1)** : `http://external-ip:PORT_LOCAL`
   - Traefik tente d'abord de router vers votre service local
   - Health check toutes les 5 secondes avec timeout de 1 seconde

2. **Service Docker (Fallback)** : `http://service-name:PORT_DOCKER`
   - Si le service local n'est pas disponible, Traefik bascule vers le conteneur Docker
   - Même configuration de health check (5s interval, 1s timeout)

### Routes OpenAPI Spéciales

Chaque service dispose également d'une route spécifique pour son endpoint OpenAPI avec middleware de suppression de préfixe :

| Service | Route OpenAPI | Middleware |
|---------|---------------|------------|
| **andoc** | `/andoc/openapi.json` | `andoc-strip-assets-prefix` |
| **bnqinvt** | `/bnqinvt/openapi.json` | `bnqinvt-strip-assets-prefix` |
| **emp** | `/emp/openapi.json` | `emp-strip-assets-prefix` |
| **traduc** | `/traduc/openapi.json` | `traduc-strip-assets-prefix` |
| **talperf** | `/talperf/openapi.json` | `talperf-strip-assets-prefix` |

**Note importante** : La route OpenAPI pour `talperftraitement` utilise le préfixe `/talperf` au lieu de `/talperftraitement`.

### Exemple de Configuration pour Développement

Pour développer localement le service `andoc` :

1. **Démarrez votre service local** sur le port `8000`
2. **Implémentez l'endpoint de santé** : `GET http://localhost:8000/health`
3. **Traefik routera automatiquement** les requêtes `/andoc/*` vers votre service local
4. **Si votre service s'arrête**, Traefik basculera automatiquement vers le conteneur Docker

### Test des Services Locaux

```powershell
# Tester la santé des services locaux directement
curl http://localhost:8000/health  # Pour andoc
curl http://localhost:8001/health  # Pour bnqinvt
curl http://localhost:8004/health  # Pour emp
curl http://localhost:8005/health  # Pour traduc
curl http://localhost:8006/health  # Pour talperftraitement
curl http://localhost:4200/        # Pour ui

# Tester via Traefik (après démarrage du proxy)
curl http://localhost/andoc/health
curl http://localhost/bnqinvt/health
curl http://localhost/emp/health
curl http://localhost/traduc/health
curl http://localhost/talperftraitement/health
curl http://localhost/              # Interface utilisateur

# Tester les endpoints OpenAPI
curl http://localhost/andoc/openapi.json
curl http://localhost/bnqinvt/openapi.json
curl http://localhost/talperf/openapi.json  # Note: /talperf et non /talperftraitement
```

### Configuration de l'external-ip

Assurez-vous que la variable `external-ip` dans votre configuration Traefik pointe vers votre machine locale. Typiquement :

- `localhost` ou `127.0.0.1` pour le développement local

## Prérequis

- **PowerShell 5.1** ou supérieur
- **Docker et Docker Compose v2+** installés et fonctionnels
- **AWS CLI** configuré (pour les commandes AWS)
- **Fichier variables.txt** configuré selon vos besoins

## Configuration AWS

Le script utilise par défaut le profil AWS : `ESG-DV-PowerUser-SSO`

### Registres supportés

- **AWS ECR** : `237029655182.dkr.ecr.ca-central-1.amazonaws.com`
- **JFrog** : `cdpq.jfrog.io`

### Commandes AWS disponibles

```powershell
# Connexion AWS SSO
.\launch.ps1 -c sso

# Vérifier l'identité AWS actuelle
.\launch.ps1 -c id

# Connexion Docker à AWS ECR
.\launch.ps1 -c dk

# Connexion Docker à JFrog
.\launch.ps1 -c jfrog
```

## Dépannage

### Problèmes d'encodage des accents

Le script configure automatiquement l'encodage UTF-8 pour l'affichage correct des caractères accentués dans PowerShell.

### Aide détaillée

Pour obtenir l'aide complète avec tous les détails :

```powershell
Get-Help .\launch.ps1 -Full
```

Ou pour l'aide simplifiée :

```powershell
.\launch.ps1 -h
```

### Logs et débogage

Pour voir les logs des services :

```powershell
docker compose logs -f [nom-du-service]
```

Pour voir l'état des services :

```powershell
docker compose ps
```
