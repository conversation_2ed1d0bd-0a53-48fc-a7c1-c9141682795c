http:
  routers:
    traefik:
      rule: "PathPrefix(`/traefik`)"
      service: "api@internal"
      priority: 1000
      entryPoints:
        - http
    andoc-openapi:
      rule: "PathPrefix(`/andoc/openapi.json`)"
      service: andoc
      middlewares:
        - andoc-strip-assets-prefix
    andoc:
      rule: "PathPrefix(`/andoc`)"
      service: andoc
    bnqinvt-openapi:
      rule: "PathPrefix(`/bnqinvt/openapi.json`)"
      service: bnqinvt
      middlewares:
        - bnqinvt-strip-assets-prefix
    bnqinvt:
      rule: "PathPrefix(`/bnqinvt`)"
      service: bnqinvt
    emp-openapi:
      rule: "PathPrefix(`/emp/openapi.json`)"
      service: emp
      middlewares:
        - emp-strip-assets-prefix
    emp:
      rule: "PathPrefix(`/emp`)"
      service: emp
    traduc-openapi:
      rule: "PathPrefix(`/traduc/openapi.json`)"
      service: traduc
      middlewares:
        - traduc-strip-assets-prefix
    traduc:
      rule: "PathPrefix(`/traduc`)"
      service: traduc
    talperf-openapi:
      rule: "PathPrefix(`/talperf/openapi.json`)"
      service: talperftraitement
      middlewares:
        - talperf-strip-assets-prefix
    talperftraitement:
      rule: "PathPrefix(`/talperftraitement`)"
      service: talperftraitement
    ui:
      rule: "PathPrefix(`/`)"
      service: ui
      priority: 1 # evaluated last
      entryPoints:
        - http

  middlewares:
    andoc-strip-assets-prefix:
      stripPrefix:
        prefixes:
          - /andoc
    talperf-strip-assets-prefix:
      stripPrefix:
        prefixes:
          - /talperf
    bnqinvt-strip-assets-prefix:
      stripPrefix:
        prefixes:
          - /bnqinvt
    emp-strip-assets-prefix:
      stripPrefix:
        prefixes:
          - /emp
    traduc-strip-assets-prefix:
      stripPrefix:
        prefixes:
          - /traduc

  services:
    andoc:
      failover:
        service: andoc-host
        fallback: andoc-docker
    andoc-host:
      loadBalancer:
        healthCheck:
          path: /health
          interval: 5s
          timeout: 1s
        servers:
          - url: http://external-ip:8000
        passHostHeader: true
    andoc-docker:
      loadBalancer:
        healthCheck:
          path: /health
          interval: 5s
          timeout: 1s
        servers:
          - url: http://andoc:8000
        passHostHeader: true
    bnqinvt:
      failover:
        service: bnqinvt-host
        fallback: bnqinvt-docker
    bnqinvt-host:
      loadBalancer:
        healthCheck:
          path: /health
          interval: 5s
          timeout: 1s
        servers:
          - url: http://external-ip:8001
        passHostHeader: true
    bnqinvt-docker:
      loadBalancer:
        healthCheck:
          path: /health
          interval: 5s
          timeout: 1s
        servers:
          - url: http://bnqinvt:8000
        passHostHeader: true
    emp:
      failover:
        service: emp-host
        fallback: emp-docker
    emp-host:
      loadBalancer:
        healthCheck:
          path: /health
          interval: 5s
          timeout: 1s
        servers:
          - url: http://external-ip:8004
        passHostHeader: true
    emp-docker:
      loadBalancer:
        healthCheck:
          path: /health
          interval: 5s
          timeout: 1s
        servers:
          - url: http://emp:8000
        passHostHeader: true

    traduc:
      failover:
        service: traduc-host
        fallback: traduc-docker
    traduc-host:
      loadBalancer:
        healthCheck:
          path: /health
          interval: 5s
          timeout: 1s
        servers:
          - url: http://external-ip:8005
        passHostHeader: true
    traduc-docker:
      loadBalancer:
        healthCheck:
          path: /health
          interval: 5s
          timeout: 1s
        servers:
          - url: http://traduc:8000
        passHostHeader: true

    talperftraitement:
      failover:
        service: talperftraitement-host
        fallback: talperftraitement-docker
    talperftraitement-host:
      loadBalancer:
        healthCheck:
          path: /health
          interval: 5s
          timeout: 1s
        servers:
          - url: http://external-ip:8006
        passHostHeader: true
    talperftraitement-docker:
      loadBalancer:
        healthCheck:
          path: /health
          interval: 5s
          timeout: 1s
        servers:
          - url: http://talperftraitement:8000
        passHostHeader: true

    ui:
      failover:
        service: ui-host
        fallback: ui-docker
    ui-host:
      loadBalancer:
        healthCheck:
          path: /
          interval: 5s
          timeout: 1s
        servers:
          - url: http://external-ip:4200
        passHostHeader: true
    ui-docker:
      loadBalancer:
        healthCheck:
          path: /
          interval: 5s
          timeout: 1s
        servers:
          - url: http://ui:80
        passHostHeader: true
