# locust -f src/locustfile.py --host=https://google.com

from locust import HttpUser, task, between, events
import json
import os

CONFIG_PATH = os.environ.get("CONFIG_PATH", "config.global.json")
with open(CONFIG_PATH, "r", encoding="utf-8") as f:
    config = json.load(f)
endpoints = config.get("endpoints", [])
jwt_token = config.get("jwt_token", "")
verify_ssl = config.get("verify_ssl", True)

class APITestUser(HttpUser):
    wait_time = between(0.5, 2)

    def on_start(self):
        self.jwt_token = jwt_token
        self.endpoints = endpoints

    @task
    def run_sequence(self):
        headers = {"X-CGPT-AUTHORIZATION": f"Bearer {self.jwt_token}"}
        for endpoint in self.endpoints:
            url = endpoint["url"]
            method = endpoint.get("method", "GET").upper()
            data = endpoint.get("data")
            try:
                if method == "GET":
                    with self.client.get(url, headers=headers, params=data, catch_response=True, verify=verify_ssl) as response:
                        self._handle_response(response, endpoint)
                elif method == "POST":
                    with self.client.post(url, headers=headers, json=data, catch_response=True, verify=verify_ssl) as response:
                        self._handle_response(response, endpoint)
                elif method == "PUT":
                    with self.client.put(url, headers=headers, json=data, catch_response=True, verify=verify_ssl) as response:
                        self._handle_response(response, endpoint)
                elif method == "DELETE":
                    with self.client.delete(url, headers=headers, json=data, catch_response=True, verify=verify_ssl) as response:
                        self._handle_response(response, endpoint)
                else:
                    print(f"Méthode HTTP non supportée: {method}")
            except Exception as e:
                print(f"Exception lors de l'appel à {url}: {e}")

    def _handle_response(self, response, endpoint):
        if response.status_code >= 400:
            response.failure(f"HTTP {response.status_code}: {response.text}")
        else:
            response.success()

@events.request.add_listener
def log_request(request_type, name, response_time, response_length, response, exception, **kwargs):
    if exception or (response and response.status_code >= 400):
        with open("logs/errors.log", "a", encoding="utf-8") as f:
            f.write(f"{name} {response.status_code if response else 'ERR'} {exception}\n")