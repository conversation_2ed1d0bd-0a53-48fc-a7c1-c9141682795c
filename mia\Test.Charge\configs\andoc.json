{"endpoints": [{"url": "/andoc/session/new", "method": "POST", "extract": [{"key": "conversation-session-1", "valueFrom": "session_id"}], "id": "creation-conversation-session-1"}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Salut", "session_id": "{conversation-session-1}"}, "depends_on": "creation-conversation-session-1", "id": "message-salut-1"}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Allo. Tu peux faire quoi pour moi ?", "session_id": "{conversation-session-1}"}, "depends_on": "creation-conversation-session-1", "id": "message-capacites-1"}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Es-tu meilleur que Copilot de Microsoft ?", "session_id": "{conversation-session-1}"}, "depends_on": "creation-conversation-session-1", "id": "message-comparaison-1"}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Qui t'a crée ? et Quand? Selon quelles procédés?", "session_id": "{conversation-session-1}"}, "depends_on": "creation-conversation-session-1", "id": "message-origine-1"}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Est-ce qu'il va y avoir des nouveautés pour mIA en 2025?", "session_id": "{conversation-session-1}"}, "depends_on": "creation-conversation-session-1", "id": "message-nouveautes-1"}, {"url": "/andoc/session/new", "method": "POST", "extract": [{"key": "conversation-id-2", "valueFrom": "session_id"}], "id": "creation-conversation-session-2"}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Salut", "session_id": "{conversation-id-2}"}, "depends_on": "creation-conversation-session-2", "id": "message-salut-2"}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Allo. Tu peux faire quoi pour moi ?", "session_id": "{conversation-id-2}"}, "depends_on": "creation-conversation-session-2", "id": "message-capacites-2"}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Es-tu meilleur que Copilot de Microsoft ?", "session_id": "{conversation-id-2}"}, "depends_on": "creation-conversation-session-2", "id": "message-comparaison-2"}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Qui t'a crée ? et Quand? Selon quelles procédés?", "session_id": "{conversation-id-2}"}, "depends_on": "creation-conversation-session-2", "id": "message-origine-2"}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Est-ce qu'il va y avoir des nouveautés pour mIA en 2025?", "session_id": "{conversation-id-2}"}, "depends_on": "creation-conversation-session-2", "id": "message-nouveautes-2"}, {"url": "/andoc/session/new", "method": "POST", "extract": [{"key": "conversation-id-3", "valueFrom": "session_id"}], "id": "creation-conversation-session-3"}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Salut", "session_id": "{conversation-id-3}"}, "depends_on": "creation-conversation-session-3", "id": "message-salut-3"}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Allo. Tu peux faire quoi pour moi ?", "session_id": "{conversation-id-3}"}, "depends_on": "creation-conversation-session-3", "id": "message-capacites-3"}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Es-tu meilleur que Copilot de Microsoft ?", "session_id": "{conversation-id-3}"}, "depends_on": "creation-conversation-session-3", "id": "message-comparaison-3"}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Qui t'a crée ? et Quand? Selon quelles procédés?", "session_id": "{conversation-id-3}"}, "depends_on": "creation-conversation-session-3", "id": "message-origine-3"}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Est-ce qu'il va y avoir des nouveautés pour mIA en 2025?", "session_id": "{conversation-id-3}"}, "depends_on": "creation-conversation-session-3", "id": "message-nouveautes-3"}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "<PERSON><PERSON><PERSON>!", "session_id": "{conversation-id-3}"}, "depends_on": "creation-conversation-session-3", "id": "message-merci-3"}, {"url": "/andoc/session/new", "method": "POST", "extract": [{"key": "session_id_analyse_1", "valueFrom": "session_id"}], "id": "creation-analyse-session-1"}, {"url": "/andoc/document/upload", "method": "POST", "depends_on": "creation-analyse-session-1", "file": "GreenStreet-REA-USIndustrialOutlook-20230117.pdf", "form_name": "file", "id": "upload-document-1", "params": {"session_id": "{session_id_analyse_1}"}}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Résume moi ce document en forme de bullet point", "is_restricted": true, "session_id": "{session_id_analyse_1}"}, "depends_on": "creation-analyse-session-1", "id": "message-resume-document-1"}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "quels sont les principaux enjeux du secteur du Bureau ?", "is_restricted": true, "session_id": "{session_id_analyse_1}"}, "depends_on": "creation-analyse-session-1", "id": "message-enjeux-1"}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Quels sont les perspectives futures du secteur du bureau", "is_restricted": true, "session_id": "{session_id_analyse_1}"}, "depends_on": "creation-analyse-session-1", "id": "message-perspective-1"}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Quels sont les marchés/villes les plus performante dans ce secteur ?", "is_restricted": true, "session_id": "{session_id_analyse_1}"}, "depends_on": "creation-analyse-session-1", "id": "message-villes-1"}, {"url": "/andoc/session/new", "method": "POST", "extract": [{"key": "session_id_analyse_2", "valueFrom": "session_id"}], "id": "creation-analyse-session-2"}, {"url": "/andoc/document/upload", "method": "POST", "depends_on": "creation-analyse-session-2", "file": "GreenStreet-REA-USIndustrialOutlook-20230117.pdf", "form_name": "file", "id": "upload-document-2", "params": {"session_id": "{session_id_analyse_2}"}}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "quels sont les principaux enjeux du secteur du Industriel ?\n", "is_restricted": true, "session_id": "{session_id_analyse_2}"}, "depends_on": "creation-analyse-session-2", "id": "message-enjeux-secteur-2"}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Quels sont les marchés/villes les plus performante dans ce secteur ?", "is_restricted": true, "session_id": "{session_id_analyse_2}"}, "depends_on": "creation-analyse-session-2", "id": "message-villes-2"}, {"url": "/andoc/session/new", "method": "POST", "extract": [{"key": "session_id_analyse_3", "valueFrom": "session_id"}], "id": "creation-analyse-session-3"}, {"url": "/andoc/document/upload", "method": "POST", "depends_on": "creation-analyse-session-3", "file": "Reponses_sondage.xlsx", "form_name": "file", "id": "upload-document-3", "params": {"session_id": "{session_id_analyse_3}"}}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Résume les principales tendances qui se dégagent des réponses au sondage. Quels sont les points de consensus et de divergence parmi les répondants ?", "is_restricted": true, "session_id": "{session_id_analyse_3}"}, "depends_on": "creation-analyse-session-3", "id": "message-tendances-3"}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Analyse les réponses aux questions ouvertes du sondage. Quels thèmes ou préoccupations récurrents émergent ? Propose une synthèse structurée.", "is_restricted": true, "session_id": "{session_id_analyse_3}"}, "depends_on": "creation-analyse-session-3", "id": "message-positives-3"}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "À partir des résultats du sondage, identifie trois axes d'amélioration prioritaires pour l'organisation. Justifie tes choix en t'appuyant sur les données.", "is_restricted": true, "session_id": "{session_id_analyse_3}"}, "depends_on": "creation-analyse-session-3", "id": "message-correlations-3"}, {"url": "/andoc/session/new", "method": "POST", "extract": [{"key": "session_id_analyse_4", "valueFrom": "session_id"}], "id": "creation-analyse-session-4"}, {"url": "/andoc/document/upload", "method": "POST", "depends_on": "creation-analyse-session-4", "file": "Reponses_sondage.xlsx", "form_name": "file", "id": "upload-document-4", "params": {"session_id": "{session_id_analyse_4}"}}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Compare les réponses obtenues selon différents sous-groupes (ex. : âge, sexe, région, secteur d'activité). Quelles différences significatives observes-tu et comment les expliques-tu ?", "is_restricted": true, "session_id": "{session_id_analyse_4}"}, "depends_on": "creation-analyse-session-4", "id": "message-segments-4"}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "Analyse les réponses aux questions ouvertes du sondage. Quels thèmes ou préoccupations récurrents émergent ? Propose une synthèse structurée.", "is_restricted": true, "session_id": "{session_id_analyse_4}"}, "depends_on": "creation-analyse-session-4", "id": "message-recommandations-4"}, {"url": "/andoc/chat/send", "method": "POST", "data": {"message": "À partir des résultats du sondage, identifie trois axes d'amélioration prioritaires pour l'organisation. Justifie tes choix en t'appuyant sur les données.", "is_restricted": true, "session_id": "{session_id_analyse_4}"}, "depends_on": "creation-analyse-session-4", "id": "message-statistiques-4"}]}